import pandas as pd
import numpy as np
import seaborn as sns 
import matplotlib as plt

house=pd.read_csv("carlifonia_housing.csv")
house

house_cpy=house.copy()

house.head()

house.duplicated()

house['Unnamed: 0'].value_counts()

house.corr()

house.head()

house.columns

house_ca=house.drop(['Unnamed: 0','Latitude','Longitude'],axis=1,inplace=True)
house_ca

house.head()

house.corr()

x=['MedInc','HouseAge','AveBedrms']
y=['MedHouseVal','AveOccup','Population']

from sklearn.model_selection import train_test_split


X_train,X_test,Y_train,Y_test=train_test_split(x,y,test_size=0.2,random_state=42)

