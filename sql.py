# import mysql.connector
# con=mydb=mysql.connector.connect(host="127.0.0.1",port="3306",user="root",password="Ashish@2804",database="test")
# print(con)
# import mysql.connector
# from mysql.connector import Error
# try:
#     con=mysql.connector.connect(host = "localhost", user = "root",passwd = "Ashish@2804", database = "mydb")
#     cursor=con.cursor()
#     query="create table student(name varchar(50),id int AUTO_INCREMENT PRIMARY KEY,age int,address varchar (50))"
#     cursor.execute(query)
#     print("Table created successfully")
# except Error as e:
#     print("There is a problem with SQL:", e)
# finally:
#     if cursor: 
#         cursor.close()
#     if con:
#         con.close()
import mysql.connector
from mysql.connector import Error

try:
    # Connect to MySQL server
    con = mysql.connector.connect(
        host="localhost",
        user="root",
        password="Ashish@2804",  
        database="test"          
    )

    if con.is_connected():
        cursor = con.cursor()
        query = """
        CREATE TABLE IF NOT EXISTS student (
            name VARCHAR(50),
            id INT AUTO_INCREMENT PRIMARY KEY,
            age INT,
            address VARCHAR(50)
        )
        """
        cursor.execute(query)
        print(" Table created successfully.")
        if con.is_connected():
            cursor = con.cursor()

        
        insert_query = "INSERT INTO student (name, age, address) VALUES (%s, %s, %s)"
        data = ("John Doe", 21, "Bhubaneswar")
        cursor.execute(insert_query, data)

        con.commit()  
        print(" Student record inserted.")
        update_query = "UPDATE student SET age = %s, address = %s WHERE name = %s"
        data = (25, "Bangalore", "Alice")  
        cursor.execute(update_query, data)
        con.commit()
        delete_query = "DELETE FROM student WHERE name = %s"
        data = ("Bob",)
        cursor.execute(delete_query, data)
        con.commit()

except Error as e:
    print(" There is a problem with SQL:", e)

finally:
    if 'cursor' in locals() and cursor:
        cursor.close()
    if 'con' in locals() and con.is_connected():
        con.close()
        print("MySQL connection closed.")
# import mysql.connector
# from mysql.connector import Error

# try:
#     con = mysql.connector.connect(
#         host="localhost",
#         user="root",
#         password="Ashish@2804",
#         database="employee_db"
#     )

#     if con.is_connected():
#         cursor = con.cursor()

#         # Insert one student record
#         insert_query = "INSERT INTO student (name, age, address) VALUES (%s, %s, %s)"
#         data = [("John Doe", 21, "Bhubaneswar")]
#         cursor.execute(insert_query, data)

#         con.commit()  # Save the change
#         print(" Student record inserted.")

# except Error as e:
#     print("Error while inserting:", e)

# finally:
#     if cursor:
#         cursor.close()
#     if con:
#         con.close()
