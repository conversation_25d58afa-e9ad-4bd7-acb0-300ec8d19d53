import mysql.connector

# 1.Connect to MySQL server
try:
    conn = mysql.connector.connect(
        host="localhost",
        user="root",    
        password="Ashish@2804",
        database="employee_db" 
    )
    cursor = conn.cursor()
    print(" Connected to MySQL server")
except mysql.connector.Error as err:
    print(" Error:", err)
    exit()

# 2. Create database if it doesn't exist
cursor.execute("CREATE DATABASE IF NOT EXISTS employee_db")
cursor.execute("USE employee_db")

# 3. Create employees table
create_table_query = """
CREATE TABLE IF NOT EXISTS employees (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100),
    position VARCHAR(50),
    salary FLOAT
)
"""
cursor.execute(create_table_query)
print("Table 'employees' is ready")

# 4. Insert at least two employee records
insert_query = "INSERT INTO employees (name, position, salary) VALUES (%s, %s, %s)"
employees = [
    ("<PERSON>", "Software Engineer", 75000.00),
    ("<PERSON>", "Data Analyst", 65000.00)
]
cursor.executemany(insert_query, employees)
conn.commit()
print("Inserted employee records")

# 5. Retrieve and display all employee records
print("\n All Employees:")
cursor.execute("SELECT * FROM employees")
for row in cursor.fetchall():
    print(row)

# 6. Search and display an employee by ID
emp_id = int(input("\n Enter employee ID to search: "))
cursor.execute("SELECT * FROM employees WHERE id = %s", (emp_id,))
result = cursor.fetchone()
if result:
    print("Employee Found:", result)
else:
    print("No employee found with ID", emp_id)

# 7. Close the database connection
cursor.close()
conn.close()
print("\n Database connection closed.")
